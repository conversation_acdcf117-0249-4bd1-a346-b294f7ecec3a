# 更新日志

所有重要的项目变更都会记录在这个文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [2.0.0] - 2024-12-XX

### 🎨 重大更新 - 全新界面设计
- **全新UI设计**：采用现代化界面，参考flomo设计风格
- **智能布局**：重新设计popup界面，分区域展示功能
- **响应式设计**：优化不同屏幕尺寸的显示效果
- **流畅交互**：添加动画效果和用户反馈

### 🔗 智能链接识别
- **自动提取**：自动识别和显示页面标题、URL
- **实时显示**：在界面顶部清晰展示页面信息
- **格式优化**：智能格式化链接显示

### 📄 AI摘要功能增强
- **一键生成**：点击按钮即可生成AI摘要
- **可编辑内容**：支持手动编辑AI生成的摘要
- **智能优化**：根据页面类型优化AI提示词
- **加载状态**：添加生成过程的视觉反馈

### 💭 个人想法记录
- **独立区域**：专门的个人想法输入区域
- **自由编辑**：支持多行文本输入和编辑
- **思维记录**：与AI摘要分离，独立管理个人思考

### 🏷️ 可视化标签系统
- **智能生成**：基于页面内容自动生成相关标签
- **可视化编辑**：直观的标签添加、编辑和删除界面
- **手动管理**：支持手动添加自定义标签
- **分类优化**：改进智能分类规则，提高标签准确性

### 🚀 一键提交功能
- **内容整合**：自动整合摘要、想法和标签
- **格式化输出**：生成结构化的笔记内容
- **快速保存**：一键提交到Blinko系统
- **状态反馈**：完整的操作状态提示

### 🔧 技术改进
- **代码重构**：优化代码结构和性能
- **消息传递**：改进background和popup之间的通信
- **错误处理**：增强异常处理和用户反馈
- **测试支持**：添加功能测试页面

### 📱 用户体验优化
- **界面宽度**：调整popup宽度，提供更好的显示空间
- **交互优化**：改进按钮和输入框的交互体验
- **视觉反馈**：添加加载动画和状态指示器
- **操作流程**：简化用户操作流程

### 🎯 破坏性变更
- **界面重构**：完全重新设计的popup界面
- **功能整合**：将多个功能整合到统一界面
- **交互方式**：改变了用户的操作流程

## [1.2.0] - 2024-12-XX

### 新增
- ✨ 硅基流动(SiliconFlow) AI服务支持
- 🔧 自定义AI模型选择功能
- 📊 高级AI参数配置（温度、Top-P、超时等）
- 📝 自定义提示词模板系统
- 🎨 折叠式高级配置界面
- 🔍 智能API地址修正功能
- 🔐 智能Token格式处理

### 改进
- 🎨 重新设计配置页面UI，提升用户体验
- 🔧 增强API连接测试功能，支持多种格式尝试
- 📱 优化响应式设计，适配不同屏幕尺寸
- 🚀 提升AI总结质量和速度
- 📋 改进错误提示和用户反馈

### 修复
- 🐛 修复API地址单复数问题
- 🔧 修复Token格式兼容性问题
- 🎯 修复自定义模型选择逻辑
- 📝 修复配置保存和加载问题

### 技术改进
- 🏗️ 重构配置管理系统
- 🔒 增强错误处理和异常捕获
- 📊 添加详细的调试信息输出
- ⚡ 优化性能和内存使用

## [1.1.0] - 2024-12-XX

### 新增
- 🤖 AI文章总结功能
- 🧠 智能内容分类系统
- ⌨️ 快捷键支持
- 🏷️ 自动标签生成
- 📊 内容质量评分
- 🔍 关键词自动提取

### 改进
- 🎨 全新的用户界面设计
- 📱 响应式布局优化
- 🚀 提升收集速度和稳定性
- 📝 增强内容格式化

### 修复
- 🐛 修复页面收集失败问题
- 🔧 修复划词收集兼容性
- 📋 修复配置同步问题

## [1.0.0] - 2024-12-XX

### 新增
- 🎉 项目首次发布
- 📝 基础网页内容收集功能
- 🔗 Blinko API集成
- ✂️ 划词收集功能
- 📌 一键页面保存
- ⚙️ 基础配置管理
- 🎯 右键菜单集成

### 技术特性
- 🏗️ Chrome扩展架构
- 🔒 安全的本地存储
- 📡 异步API调用
- 🎨 现代化UI设计

---

## 版本说明

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

## 图标说明

- ✨ 新增功能
- 🔧 功能改进
- 🐛 Bug修复
- 🎨 UI/UX改进
- 📝 文档更新
- 🚀 性能优化
- 🔒 安全性改进
- 🏗️ 架构变更
- 📊 数据/分析相关
- ⚡ 性能提升