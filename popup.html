<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 420px;
      padding: 0;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #2c3e50;
    }
    .container {
      background: rgba(255, 255, 255, 0.98);
      border-radius: 0;
      padding: 0;
      min-height: 600px;
      display: flex;
      flex-direction: column;
    }

    /* 头部样式 */
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px 20px;
      display: flex;
      align-items: center;
      gap: 12px;
    }
    .header-icon {
      font-size: 24px;
    }
    .header-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }
    .settings-btn {
      margin-left: auto;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 8px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.2s ease;
    }
    .settings-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.05);
    }

    /* 链接信息区域 */
    .link-section {
      padding: 16px 20px;
      border-bottom: 1px solid #e1e8ed;
      background: #f8f9fa;
    }
    .link-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      color: #495057;
      font-size: 14px;
      font-weight: 500;
    }
    .page-title-display {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      line-height: 1.4;
      word-break: break-word;
    }
    .page-url-display {
      font-size: 12px;
      color: #6c757d;
      background: white;
      padding: 8px 12px;
      border-radius: 6px;
      border: 1px solid #e1e8ed;
      word-break: break-all;
      font-family: 'Monaco', 'Menlo', monospace;
    }

    /* 原文摘要区域 */
    .summary-section {
      padding: 16px 20px;
      border-bottom: 1px solid #e1e8ed;
    }
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #495057;
      font-size: 14px;
      font-weight: 500;
    }
    .ai-generate-btn {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    .ai-generate-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }
    .summary-content {
      background: white;
      border: 1px solid #e1e8ed;
      border-radius: 8px;
      padding: 12px;
      min-height: 80px;
      font-size: 14px;
      line-height: 1.5;
      resize: vertical;
      width: 100%;
      box-sizing: border-box;
      font-family: inherit;
    }
    .summary-content:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }
    .summary-placeholder {
      color: #adb5bd;
      font-style: italic;
    }

    /* 个人想法区域 */
    .thoughts-section {
      padding: 16px 20px;
      border-bottom: 1px solid #e1e8ed;
    }
    .thoughts-content {
      background: white;
      border: 1px solid #e1e8ed;
      border-radius: 8px;
      padding: 12px;
      min-height: 60px;
      font-size: 14px;
      line-height: 1.5;
      resize: vertical;
      width: 100%;
      box-sizing: border-box;
      font-family: inherit;
    }
    .thoughts-content:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }

    /* 标签区域 */
    .tags-section {
      padding: 16px 20px;
      border-bottom: 1px solid #e1e8ed;
    }
    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 8px;
    }
    .tag {
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .tag-remove {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 14px;
      padding: 0;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .tag-remove:hover {
      background: rgba(255, 255, 255, 0.2);
    }
    .tag-input {
      border: 1px solid #e1e8ed;
      border-radius: 4px;
      padding: 6px 8px;
      font-size: 12px;
      width: 120px;
    }
    .tag-input:focus {
      outline: none;
      border-color: #667eea;
    }

    /* 底部操作区域 */
    .actions-section {
      padding: 20px;
      margin-top: auto;
    }
    .submit-btn {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      width: 100%;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    .submit-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }
    .submit-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    /* 状态提示 */
    .status {
      padding: 8px 12px;
      margin: 8px 0;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      text-align: center;
    }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

    /* 加载动画 */
    .loading {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid #667eea;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s ease-in-out infinite;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    /* 隐藏类 */
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 头部 -->
    <div class="header">
      <span class="header-icon">🤖</span>
      <h1 class="header-title">善思 blinko</h1>
      <button class="settings-btn" id="configBtn" title="配置设置">⚙️</button>
    </div>

    <!-- 链接信息区域 -->
    <div class="link-section">
      <div class="link-header">
        <span>🔗</span>
        <span>标题和链接</span>
      </div>
      <div id="pageTitle" class="page-title-display">加载中...</div>
      <div id="pageUrl" class="page-url-display"></div>
    </div>

    <!-- 原文摘要区域 -->
    <div class="summary-section">
      <div class="section-header">
        <div class="section-title">
          <span>📄</span>
          <span>原文摘要</span>
        </div>
        <button class="ai-generate-btn" id="aiGenerateBtn">
          <span class="loading hidden" id="aiLoading"></span>
          <span id="aiGenerateText">AI总结</span>
        </button>
      </div>
      <textarea
        id="summaryContent"
        class="summary-content"
        placeholder="粘贴原文摘要..."
        rows="4"
      ></textarea>
    </div>

    <!-- 个人想法区域 -->
    <div class="thoughts-section">
      <div class="section-title">
        <span>💭</span>
        <span>个人想法</span>
      </div>
      <textarea
        id="thoughtsContent"
        class="thoughts-content"
        placeholder="输入你的想法..."
        rows="3"
      ></textarea>
    </div>

    <!-- 标签区域 -->
    <div class="tags-section">
      <div class="section-title">
        <span>🏷️</span>
        <span>标签</span>
      </div>
      <div id="tagsContainer" class="tags-container">
        <!-- 标签将在这里动态生成 -->
      </div>
      <input
        type="text"
        id="tagInput"
        class="tag-input"
        placeholder="添加标签..."
        style="display: none;"
      >
    </div>

    <!-- 底部操作区域 -->
    <div class="actions-section">
      <button id="submitBtn" class="submit-btn">
        <span>🤖</span>
        <span>提交到 Blinko</span>
      </button>
      <div id="status"></div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>